from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

router = Router()

# Импортируем менеджер навигации
from common.register_handlers_and_transitions import navigation_manager

@router.callback_query(F.data == "back")
async def go_back(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    await navigation_manager.handle_back(callback, state, user_role)

# Регистрация обработчика кнопки "Главное меню"
@router.callback_query(F.data == "back_to_main")
async def back_to_main_handler(callback: CallbackQuery, state: FSMContext, user_role: str):
    await navigation_manager.handle_main_menu(callback, state, user_role)


async def check_if_id_in_callback_data(callback_starts_with: str, callback: CallbackQuery, state: F<PERSON><PERSON>ontext, id_type)-> str:
    # Проверяем, является ли callback.data ID группы или это кнопка "назад"
    if callback.data.startswith(callback_starts_with):
        id = callback.data.replace(callback_starts_with, "")
        print(f"{id_type}_id: ", id)
        await state.update_data(**{id_type:id})
    else:
        # Если это кнопка "назад" или другой callback, берем ID из состояния
        user_data = await state.get_data()
        id = user_data.get(id_type)
        print(f"Using saved {id_type}_id: ", id)
    return id
